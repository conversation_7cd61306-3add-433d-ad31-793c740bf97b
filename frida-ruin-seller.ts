/**
 * Frida Ruin Seller - Standalone automated ruin selling script for Dominations
 *
 * This script automatically discovers and processes GoodyHut instances with sellable ruins,
 * calling the SellRuins() method (Token 0x6002B93, Address RVA "0x209DE3C") to clear completed instances.
 *
 * Usage: frida -U -l frida-ruin-seller.js com.nexonm.dominations.adk
 */

// Frida script initialization
console.log("🗑️ Frida Ruin Seller v1.0 - Starting initialization...");

// Frida type declarations
declare const Java: any;
declare const Il2Cpp: {
    domain: {
        assembly(name: string): {
            image: any;
        };
    };
    gc: {
        choose(clazz: any): any[];
    };
    perform(fn: () => void): void;
};

// Type definitions for Il2Cpp interop
interface Il2CppObject {
    handle: NativePointer;
    class: Il2CppClass;
    isNull(): boolean;
    method(name: string): Il2CppMethod | null;
    field(name: string): Il2CppField | null;
}

interface Il2CppClass {
    name: string;
    handle: NativePointer;
    methods: Il2CppMethod[];
    fields: Il2CppField[];
}

interface Il2CppMethod {
    name: string;
    handle: NativePointer;
    invoke(...args: any[]): any;
}

interface Il2CppField {
    name: string;
    type: string;
    value: any;
}

interface ProcessingStats {
    totalInstances: number;
    validGoodyHuts: number;
    completedInstances: number;
    ruinSellAttempts: number;
    successfulSells: number;
    failedSells: number;
    startTime: number;
    endTime?: number;
    executionTimeMs?: number;
}

interface InstanceValidation {
    isValid: boolean;
    hasGoodyHut: boolean;
    isCompleted: boolean;
    hasRuins: boolean;
    canSell: boolean;
    state: string;
    rewardType: string;
    rewardAmount: number | null;
    error?: string;
}

class FridaRuinSeller {
    private stats: ProcessingStats;
    private assemblyImage: any;
    private entityControllerClass: any;

    constructor() {
        this.stats = {
            totalInstances: 0,
            validGoodyHuts: 0,
            completedInstances: 0,
            ruinSellAttempts: 0,
            successfulSells: 0,
            failedSells: 0,
            startTime: Date.now()
        };
    }

    /**
     * Wait for Il2Cpp bridge to be available
     */
    private async waitForIl2Cpp(maxWaitMs: number = 10000): Promise<boolean> {
        const startTime = Date.now();
        let attempts = 0;

        console.log("⏳ Checking Il2Cpp bridge availability...");

        while (Date.now() - startTime < maxWaitMs) {
            attempts++;

            try {
                // Debug: Log what's available
                if (attempts === 1) {
                    console.log(`🔍 Il2Cpp type: ${typeof Il2Cpp}`);
                    if (typeof Il2Cpp !== 'undefined') {
                        console.log(`🔍 Il2Cpp object: ${Il2Cpp}`);
                        console.log(`🔍 Il2Cpp.domain: ${Il2Cpp.domain}`);
                        console.log(`🔍 Il2Cpp.gc: ${Il2Cpp.gc}`);
                    }
                }

                // Check if Il2Cpp is defined and has required properties
                if (typeof Il2Cpp !== 'undefined' &&
                    Il2Cpp &&
                    Il2Cpp.domain &&
                    Il2Cpp.gc) {

                    // Additional check: try to access domain
                    try {
                        const testAssembly = Il2Cpp.domain.assembly("Assembly-CSharp");
                        if (testAssembly && testAssembly.image) {
                            console.log("✅ Il2Cpp bridge is fully available and functional");
                            return true;
                        }
                    } catch (domainError) {
                        console.log(`⚠️ Il2Cpp domain not ready: ${domainError}`);
                    }
                }
            } catch (error) {
                if (attempts === 1) {
                    console.log(`⚠️ Il2Cpp check error: ${error}`);
                }
            }

            // Progress indicator every 2 seconds
            if (attempts % 20 === 0) {
                const elapsed = Date.now() - startTime;
                console.log(`⏳ Still waiting for Il2Cpp bridge... (${elapsed}ms elapsed)`);
            }

            // Wait 100ms before next check
            await new Promise(resolve => setTimeout(resolve, 100));
        }

        console.log("❌ Il2Cpp bridge not available after timeout");
        return false;
    }

    /**
     * Initialize Il2Cpp domain and get required classes
     */
    private async initialize(): Promise<boolean> {
        try {
            console.log("🔧 Waiting for Il2Cpp bridge...");

            // Wait for Il2Cpp to be available
            if (!(await this.waitForIl2Cpp())) {
                console.log("❌ Il2Cpp bridge not available");
                return false;
            }

            console.log("🔧 Initializing Il2Cpp domain...");

            // Get Assembly-CSharp image
            this.assemblyImage = Il2Cpp.domain.assembly("Assembly-CSharp").image;
            if (!this.assemblyImage) {
                console.log("❌ Failed to get Assembly-CSharp image");
                return false;
            }

            // Get EntityController class
            this.entityControllerClass = this.assemblyImage.class("EntityController");
            if (!this.entityControllerClass) {
                console.log("❌ Failed to get EntityController class");
                return false;
            }

            console.log("✅ Il2Cpp domain initialized successfully");
            return true;
        } catch (error) {
            console.log(`❌ Initialization failed: ${error}`);
            return false;
        }
    }

    /**
     * Safe method invocation with parameter discovery and error handling
     */
    private safeInvoke(instance: any, methodName: string, ...args: any[]): { error: string | null, value: any } {
        try {
            // Check if instance is null or invalid
            if (!instance || instance.isNull()) {
                return { error: "Null instance", value: null };
            }
            
            // Try to get the method
            const method = instance.method(methodName);
            if (!method) {
                return { error: `Method ${methodName} not found`, value: null };
            }
            
            let result: any;
            
            try {
                // First try with provided arguments
                if (args.length > 0) {
                    result = (method as any).invoke(...args);
                } else {
                    result = method.invoke();
                }
            } catch (paramError: any) {
                const paramErrorMsg = String(paramError);
                
                // Handle "bad argument count" errors with parameter discovery
                if (paramErrorMsg.includes("bad argument count")) {
                    // Try common parameter patterns
                    const parameterAttempts = [
                        [], // No parameters
                        [true], // Boolean parameter
                        [false], // Boolean parameter (opposite)
                        [0], // Integer parameter
                        [1], // Integer parameter
                        [null], // Null parameter
                        [true, 0], // Boolean + integer
                        [false, 0], // Boolean + integer
                    ];
                    
                    for (const params of parameterAttempts) {
                        try {
                            result = (method as any).invoke(...params);
                            // Success - log working parameters for debugging
                            if (params.length > 0) {
                                console.log(`✅ ${methodName} succeeded with parameters: [${params.join(', ')}]`);
                            }
                            break;
                        } catch (attemptError) {
                            continue; // Try next parameter combination
                        }
                    }
                    
                    // If all attempts failed, return method-specific safe defaults
                    if (result === undefined) {
                        if (methodName === "CanCollect" || methodName.includes("Can")) {
                            return { error: null, value: false };
                        } else if (methodName === "IsJobComplete" || methodName.includes("Is")) {
                            return { error: null, value: false };
                        } else if (methodName.includes("Get") && methodName.includes("Amount")) {
                            return { error: null, value: 0 };
                        } else if (methodName.includes("Get") && methodName.includes("Type")) {
                            return { error: null, value: "UNKNOWN" };
                        } else {
                            return { error: `Parameter error: ${paramErrorMsg}`, value: null };
                        }
                    }
                } else {
                    throw paramError; // Re-throw non-parameter errors
                }
            }
            
            return { error: null, value: result };
            
        } catch (error) {
            const errorMsg = String(error);
            if (errorMsg.includes("access violation") || errorMsg.includes("0x0")) {
                return { error: "Access violation - invalid instance", value: null };
            }
            return { error: `Method error: ${errorMsg}`, value: null };
        }
    }

    /**
     * Validate if an EntityController instance has a GoodyHut with sellable ruins
     */
    private validateInstance(instance: any, index: number): InstanceValidation {
        const validation: InstanceValidation = {
            isValid: false,
            hasGoodyHut: false,
            isCompleted: false,
            hasRuins: false,
            canSell: false,
            state: "UNKNOWN",
            rewardType: "UNKNOWN",
            rewardAmount: null
        };

        try {
            // Check if instance has GoodyHut component
            const goodyHutField = instance.field("m_goodyHut");
            if (!goodyHutField || !goodyHutField.value || goodyHutField.value.toString() === "0x0") {
                validation.error = "No GoodyHut component";
                return validation;
            }

            validation.hasGoodyHut = true;
            const goodyHutInstance = goodyHutField.value as any;

            // Check if job is complete
            const isCompleteResult = this.safeInvoke(goodyHutInstance, "IsJobComplete");
            if (isCompleteResult.error) {
                validation.error = `IsJobComplete failed: ${isCompleteResult.error}`;
                return validation;
            }

            validation.isCompleted = isCompleteResult.value === true;

            // Check if can collect (indicates completed state)
            const canCollectResult = this.safeInvoke(goodyHutInstance, "CanCollect");
            if (!canCollectResult.error && canCollectResult.value === true) {
                validation.state = "COMPLETED_AWAITING";
            } else if (validation.isCompleted) {
                validation.state = "COMPLETED";
            } else {
                validation.state = "COLLECTING";
            }

            // Get reward information
            const rewardTypeResult = this.safeInvoke(goodyHutInstance, "GetRewardType");
            const rewardAmountResult = this.safeInvoke(goodyHutInstance, "GetRewardAmount");

            if (!rewardTypeResult.error) {
                validation.rewardType = rewardTypeResult.value?.toString() || "UNKNOWN";
            }
            if (!rewardAmountResult.error) {
                validation.rewardAmount = rewardAmountResult.value || 0;
            }

            // Check for sellable ruins (multiple methods to try)
            const ruinCheckMethods = ["HasRuins", "HasDebris", "CanSell", "CanClear"];
            for (const methodName of ruinCheckMethods) {
                const result = this.safeInvoke(goodyHutInstance, methodName);
                if (!result.error && result.value === true) {
                    validation.hasRuins = true;
                    validation.canSell = true;
                    break;
                }
            }

            // Instance is valid if it has a GoodyHut and is completed with potential ruins
            validation.isValid = validation.hasGoodyHut && 
                                (validation.isCompleted || validation.state === "COMPLETED_AWAITING");

            return validation;

        } catch (error) {
            validation.error = `Validation error: ${error}`;
            return validation;
        }
    }

    /**
     * Execute SellRuins method on a validated instance
     */
    private executeSellRuins(instance: any, validation: InstanceValidation, index: number): boolean {
        try {
            console.log(`🗑️ [${index}] Attempting to sell ruins...`);

            const goodyHutField = instance.field("m_goodyHut");
            if (!goodyHutField || !goodyHutField.value) {
                console.log(`❌ [${index}] GoodyHut component not accessible`);
                return false;
            }

            const goodyHutInstance = goodyHutField.value as any;

            // Try different ruin selling methods
            const sellMethods = ["SellRuins", "ClearDebris", "DoSell", "DoClear"];
            
            for (const methodName of sellMethods) {
                const sellResult = this.safeInvoke(goodyHutInstance, methodName);
                
                if (!sellResult.error) {
                    console.log(`✅ [${index}] Successfully executed ${methodName}`);
                    
                    // Verify the operation by checking state change
                    setTimeout(() => {
                        const postValidation = this.validateInstance(instance, index);
                        if (postValidation.state !== validation.state) {
                            console.log(`🎉 [${index}] State changed: ${validation.state} → ${postValidation.state}`);
                        }
                    }, 500);
                    
                    return true;
                } else if (!sellResult.error.includes("not found")) {
                    console.log(`⚠️ [${index}] ${methodName} failed: ${sellResult.error}`);
                }
            }

            console.log(`❌ [${index}] No working sell method found`);
            return false;

        } catch (error) {
            console.log(`❌ [${index}] Sell execution error: ${error}`);
            return false;
        }
    }

    /**
     * Discover all EntityController instances using Il2Cpp garbage collector
     */
    private discoverInstances(): any[] {
        try {
            console.log("🔍 Discovering EntityController instances...");

            const entityInstances = Il2Cpp.gc.choose(this.entityControllerClass);

            if (!entityInstances || entityInstances.length === 0) {
                console.log("❌ No EntityController instances found");
                return [];
            }

            console.log(`✅ Discovered ${entityInstances.length} EntityController instances`);
            this.stats.totalInstances = entityInstances.length;

            return entityInstances;

        } catch (error) {
            console.log(`❌ Instance discovery failed: ${error}`);
            return [];
        }
    }

    /**
     * Process all instances for ruin selling
     */
    private async processAllInstances(): Promise<void> {
        const instances = this.discoverInstances();
        if (instances.length === 0) {
            return;
        }

        console.log("🔍 Filtering instances for GoodyHut components with sellable ruins...");
        const validInstances: { instance: any, validation: InstanceValidation, index: number }[] = [];

        // First pass: validate all instances
        for (let i = 0; i < instances.length; i++) {
            const instance = instances[i];
            const validation = this.validateInstance(instance, i);

            if (validation.hasGoodyHut) {
                this.stats.validGoodyHuts++;
            }

            if (validation.isCompleted) {
                this.stats.completedInstances++;
            }

            // Include instances that are completed and potentially have ruins
            if (validation.isValid && (validation.hasRuins || validation.isCompleted)) {
                validInstances.push({ instance, validation, index: i });
                console.log(`📋 [${i}] Queued: ${validation.state} (${validation.rewardType}: ${validation.rewardAmount})`);
            }

            // Progress update every 1000 instances
            if ((i + 1) % 1000 === 0) {
                const progress = ((i + 1) / instances.length * 100).toFixed(1);
                console.log(`📊 Validation progress: ${progress}% (${i + 1}/${instances.length})`);
            }
        }

        console.log(`🎯 Found ${validInstances.length} instances ready for ruin selling`);
        console.log(`📊 Summary: ${this.stats.validGoodyHuts} GoodyHuts, ${this.stats.completedInstances} completed`);

        if (validInstances.length === 0) {
            console.log("ℹ️ No instances found with sellable ruins");
            return;
        }

        // Second pass: execute ruin selling
        console.log("🗑️ Starting ruin selling operations...");

        for (let i = 0; i < validInstances.length; i++) {
            const { instance, validation, index } = validInstances[i];

            console.log(`🗑️ Processing ${i + 1}/${validInstances.length}: EntityController ${index}`);
            console.log(`   State: ${validation.state}, Reward: ${validation.rewardAmount} ${validation.rewardType}`);

            this.stats.ruinSellAttempts++;

            const success = this.executeSellRuins(instance, validation, index);

            if (success) {
                this.stats.successfulSells++;
                console.log(`✅ [${index}] Ruin selling completed successfully`);
            } else {
                this.stats.failedSells++;
                console.log(`❌ [${index}] Ruin selling failed`);
            }

            // Small delay between operations to avoid overwhelming the system
            if (i < validInstances.length - 1) {
                await new Promise(resolve => setTimeout(resolve, 200));
            }

            // Progress update every 10 operations
            if ((i + 1) % 10 === 0) {
                const progress = ((i + 1) / validInstances.length * 100).toFixed(1);
                console.log(`📊 Processing progress: ${progress}% (${i + 1}/${validInstances.length})`);
            }
        }
    }

    /**
     * Generate final statistics report
     */
    private generateReport(): void {
        this.stats.endTime = Date.now();
        this.stats.executionTimeMs = this.stats.endTime - this.stats.startTime;

        console.log("\n" + "=".repeat(60));
        console.log("🗑️ FRIDA RUIN SELLER - EXECUTION REPORT");
        console.log("=".repeat(60));
        console.log(`📊 Instance Discovery:`);
        console.log(`   Total Instances: ${this.stats.totalInstances}`);
        console.log(`   Valid GoodyHuts: ${this.stats.validGoodyHuts}`);
        console.log(`   Completed Instances: ${this.stats.completedInstances}`);
        console.log("");
        console.log(`🗑️ Ruin Selling Operations:`);
        console.log(`   Attempts: ${this.stats.ruinSellAttempts}`);
        console.log(`   Successful: ${this.stats.successfulSells}`);
        console.log(`   Failed: ${this.stats.failedSells}`);

        if (this.stats.ruinSellAttempts > 0) {
            const successRate = (this.stats.successfulSells / this.stats.ruinSellAttempts * 100).toFixed(1);
            console.log(`   Success Rate: ${successRate}%`);
        }

        console.log("");
        console.log(`⏱️ Performance Metrics:`);
        console.log(`   Execution Time: ${this.stats.executionTimeMs}ms (${(this.stats.executionTimeMs! / 1000).toFixed(2)}s)`);

        if (this.stats.ruinSellAttempts > 0 && this.stats.executionTimeMs) {
            const opsPerSecond = (this.stats.ruinSellAttempts / (this.stats.executionTimeMs / 1000)).toFixed(2);
            console.log(`   Operations/Second: ${opsPerSecond}`);
        }

        console.log("=".repeat(60));

        if (this.stats.successfulSells > 0) {
            console.log(`✅ Successfully processed ${this.stats.successfulSells} ruin selling operations!`);
        } else {
            console.log(`ℹ️ No ruin selling operations were completed.`);
        }

        console.log("🗑️ Frida Ruin Seller execution completed.");
    }

    /**
     * Main execution method
     */
    public async run(): Promise<void> {
        try {
            console.log("🗑️ Frida Ruin Seller - Starting automated ruin selling...");

            // Initialize Il2Cpp domain
            if (!(await this.initialize())) {
                console.log("❌ Initialization failed - aborting execution");
                return;
            }

            // Process all instances
            await this.processAllInstances();

            // Generate final report
            this.generateReport();

        } catch (error) {
            console.log(`❌ Fatal error during execution: ${error}`);
            this.generateReport();
        }
    }
}

// Script execution entry point with multiple initialization strategies
async function initializeAndRun() {
    console.log("🚀 Frida Ruin Seller - Initializing...");

    try {
        // Strategy 1: Try Il2Cpp.perform() if available
        if (typeof Il2Cpp !== 'undefined' && Il2Cpp && Il2Cpp.perform) {
            console.log("✅ Using Il2Cpp.perform() context");
            Il2Cpp.perform(async () => {
                const ruinSeller = new FridaRuinSeller();
                await ruinSeller.run();
            });
            return;
        }

        // Strategy 2: Try Java.perform() with Il2Cpp availability check
        if (typeof Java !== 'undefined' && Java && Java.perform) {
            console.log("✅ Using Java.perform() context with Il2Cpp bridge");
            Java.perform(async () => {
                const ruinSeller = new FridaRuinSeller();
                await ruinSeller.run();
            });
            return;
        }

        // Strategy 3: Direct execution (fallback)
        console.log("⚠️ Using direct execution (no perform context available)");
        const ruinSeller = new FridaRuinSeller();
        await ruinSeller.run();

    } catch (error) {
        console.log(`❌ Initialization strategy failed: ${error}`);

        // Final fallback: try direct execution after delay
        console.log("🔄 Attempting fallback execution...");
        setTimeout(async () => {
            try {
                const ruinSeller = new FridaRuinSeller();
                await ruinSeller.run();
            } catch (fallbackError) {
                console.log(`❌ Fallback execution failed: ${fallbackError}`);
            }
        }, 2000);
    }
}

// Start initialization with multiple timing strategies
console.log("🔄 Scheduling script execution...");

// Immediate attempt
setTimeout(() => {
    initializeAndRun().catch(error => {
        console.log(`❌ Immediate execution failed: ${error}`);
    });
}, 500);

// Delayed attempt as backup
setTimeout(() => {
    console.log("🔄 Backup execution attempt...");
    initializeAndRun().catch(error => {
        console.log(`❌ Backup execution failed: ${error}`);
    });
}, 5000);

// Export for potential external usage
(globalThis as any).FridaRuinSeller = FridaRuinSeller;

console.log("🗑️ Frida Ruin Seller script loaded successfully - execution will begin shortly...");
